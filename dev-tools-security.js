/**
 * 开发者工具安全控制模块
 * 在生产环境下禁用所有可能打开开发者工具的方式
 */

/**
 * 为窗口设置开发者工具安全限制
 * @param {BrowserWindow} mainWindow - 主窗口实例
 */
function setupDevToolsSecurity(mainWindow) {
  // 禁用右键菜单
  mainWindow.webContents.on('context-menu', (e) => {
    e.preventDefault();
  });

  // 禁用快捷键打开开发者工具
  mainWindow.webContents.on('before-input-event', (event, input) => {
    // 禁用 F12
    if (input.key === 'F12') {
      event.preventDefault();
    }

    // 禁用 Ctrl+Shift+I (Windows/Linux) 和 Cmd+Option+I (Mac)
    if ((input.control || input.meta) && input.shift && input.key === 'I') {
      event.preventDefault();
    }

    // 禁用 Ctrl+Shift+J (Windows/Linux) 和 Cmd+Option+J (Mac)
    if ((input.control || input.meta) && input.shift && input.key === 'J') {
      event.preventDefault();
    }

    // 禁用 Ctrl+Shift+C (Windows/Linux) 和 Cmd+Option+C (Mac)
    if ((input.control || input.meta) && input.shift && input.key === 'C') {
      event.preventDefault();
    }

    // 禁用 Ctrl+U (Windows/Linux) 和 Cmd+U (Mac) - 查看源代码
    if ((input.control || input.meta) && input.key === 'U') {
      event.preventDefault();
    }
  });

  // 阻止新窗口打开（防止通过新窗口绕过限制）
  mainWindow.webContents.setWindowOpenHandler(() => {
    return { action: 'deny' };
  });

  // 监听开发者工具打开事件并立即关闭
  mainWindow.webContents.on('devtools-opened', () => {
    mainWindow.webContents.closeDevTools();
  });
}

module.exports = { setupDevToolsSecurity };
