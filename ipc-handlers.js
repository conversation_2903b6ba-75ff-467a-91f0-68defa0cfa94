const { ipcMain } = require('electron');
const { exec } = require('child_process');
const path = require('path');
const fs = require('fs').promises;
const os = require('os');
const sqlite3 = require('sqlite3');
const archiver = require('archiver');

/**
 * 注册所有IPC处理器
 */
function setupIPC() {
  // IPC处理器：执行系统脚本
  ipcMain.handle('execute-script', async (event, script) => {
    return new Promise((resolve) => {
      // 仅在Mac系统上执行
      if (process.platform !== 'darwin') {
        resolve({
          success: false,
          output: '',
          error: '此功能仅支持Mac系统'
        });
        return;
      }

      // 执行bash脚本
      exec(script, { shell: '/bin/bash', timeout: 30000 }, (error, stdout, stderr) => {
        resolve({
          success: !error,
          output: stdout || '',
          error: stderr || (error ? error.message : '')
        });
      });
    });
  });

  // Augment重置相关IPC处理器
  // 获取用户主目录
  ipcMain.handle('get-home-directory', async () => {
    return os.homedir();
  });

  // 获取应用数据目录
  ipcMain.handle('get-app-data-directory', async () => {
    if (process.platform === 'win32') {
      return process.env.APPDATA || '';
    } else if (process.platform === 'darwin') {
      return path.join(os.homedir(), 'Library/Application Support');
    } else {
      return path.join(os.homedir(), '.local/share');
    }
  });

  // 检查文件是否存在
  ipcMain.handle('file-exists', async (event, filePath) => {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  });

  // 检查目录是否存在
  ipcMain.handle('directory-exists', async (event, dirPath) => {
    try {
      const stats = await fs.stat(dirPath);
      return stats.isDirectory();
    } catch {
      return false;
    }
  });

  // 创建文件备份
  ipcMain.handle('create-file-backup', async (event, filePath, backupPath) => {
    try {
      await fs.copyFile(filePath, backupPath);
      return true;
    } catch (error) {
      throw new Error(`创建备份失败: ${error.message}`);
    }
  });

  // 读取JSON文件
  ipcMain.handle('read-json-file', async (event, filePath) => {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      throw new Error(`读取JSON文件失败: ${error.message}`);
    }
  });

  // 写入JSON文件
  ipcMain.handle('write-json-file', async (event, filePath, data) => {
    try {
      const jsonString = JSON.stringify(data, null, 4);
      await fs.writeFile(filePath, jsonString, 'utf8');
      return true;
    } catch (error) {
      throw new Error(`写入JSON文件失败: ${error.message}`);
    }
  });

  // 写入文本文件
  ipcMain.handle('write-text-file', async (event, filePath, content) => {
    try {
      await fs.writeFile(filePath, content, 'utf8');
      return true;
    } catch (error) {
      throw new Error(`写入文本文件失败: ${error.message}`);
    }
  });

  // 清理数据库中的Augment数据
  ipcMain.handle('clean-augment-data-from-db', async (event, dbPath) => {
    return new Promise((resolve, reject) => {
      const db = new sqlite3.Database(dbPath, (err) => {
        if (err) {
          reject(new Error(`打开数据库失败: ${err.message}`));
          return;
        }

        db.run("DELETE FROM ItemTable WHERE key LIKE '%augment%'", function(err) {
          if (err) {
            db.close();
            reject(new Error(`删除数据失败: ${err.message}`));
            return;
          }

          const deletedRows = this.changes;
          db.close((err) => {
            if (err) {
              reject(new Error(`关闭数据库失败: ${err.message}`));
              return;
            }
            resolve(deletedRows);
          });
        });
      });
    });
  });

  // 创建工作区备份
  ipcMain.handle('create-workspace-backup', async (event, workspacePath, backupPath) => {
    return new Promise((resolve, reject) => {
      const output = require('fs').createWriteStream(backupPath);
      const archive = archiver('zip', {
        zlib: { level: 9 }
      });

      output.on('close', () => {
        resolve(true);
      });

      archive.on('error', (err) => {
        reject(new Error(`创建备份失败: ${err.message}`));
      });

      archive.pipe(output);
      archive.directory(workspacePath, false);
      archive.finalize();
    });
  });

  // 清理工作区目录
  ipcMain.handle('clean-workspace-directory', async (event, workspacePath) => {
    try {
      // 统计文件数量
      let fileCount = 0;

      async function countFiles(dir) {
        const entries = await fs.readdir(dir, { withFileTypes: true });
        for (const entry of entries) {
          if (entry.isFile()) {
            fileCount++;
          } else if (entry.isDirectory()) {
            await countFiles(path.join(dir, entry.name));
          }
        }
      }

      await countFiles(workspacePath);

      // 删除目录内容
      const entries = await fs.readdir(workspacePath);
      for (const entry of entries) {
        const fullPath = path.join(workspacePath, entry);
        const stats = await fs.stat(fullPath);

        if (stats.isDirectory()) {
          await fs.rm(fullPath, { recursive: true, force: true });
        } else {
          await fs.unlink(fullPath);
        }
      }

      return fileCount;
    } catch (error) {
      throw new Error(`清理工作区目录失败: ${error.message}`);
    }
  });
}

module.exports = { setupIPC };
