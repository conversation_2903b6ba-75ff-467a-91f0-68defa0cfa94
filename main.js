const { app, BrowserWindow } = require('electron');
const path = require('path');
const { setupIPC } = require('./ipc-handlers');
const { setupDevToolsSecurity } = require('./dev-tools-security');

// 检查是否为开发模式
const isDev = process.argv.includes('--dev') || process.env.NODE_ENV === 'development';

// 创建主窗口
function createWindow() {
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true,
      // 生产环境下禁用开发者工具
      devTools: isDev
    }
  });

  // 加载应用的index.html
  mainWindow.loadFile('index.html');

  // 开发模式下打开开发者工具
  if (isDev) {
    mainWindow.webContents.openDevTools();
  }

  // 生产环境下禁用所有可能打开开发者工具的方式
  if (!isDev) {
    setupDevToolsSecurity(mainWindow);
  }
}

// 当Electron完成初始化并准备创建浏览器窗口时调用此方法
app.whenReady().then(() => {
  // 先设置IPC处理器
  setupIPC();

  // 然后创建窗口
  createWindow();

  app.on('activate', () => {
    // 在macOS上，当点击dock图标并且没有其他窗口打开时，
    // 通常在应用程序中重新创建一个窗口
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// 当所有窗口都关闭时退出应用
app.on('window-all-closed', () => {
  // 在macOS上，应用程序和它们的菜单栏通常保持活动状态，
  // 直到用户使用Cmd + Q明确退出
  if (process.platform !== 'darwin') app.quit();
});
