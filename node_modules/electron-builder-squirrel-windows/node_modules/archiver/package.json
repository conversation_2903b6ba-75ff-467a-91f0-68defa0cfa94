{"name": "archiver", "version": "5.3.2", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/archiverjs/node-archiver.git"}, "bugs": {"url": "https://github.com/archiverjs/node-archiver/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">= 10"}, "scripts": {"test": "mocha --reporter dot", "bench": "node benchmark/simple/pack-zip.js"}, "dependencies": {"archiver-utils": "^2.1.0", "async": "^3.2.4", "buffer-crc32": "^0.2.1", "readable-stream": "^3.6.0", "readdir-glob": "^1.1.2", "tar-stream": "^2.2.0", "zip-stream": "^4.1.0"}, "devDependencies": {"archiver-jsdoc-theme": "^1.1.3", "chai": "^4.3.7", "jsdoc": "^3.6.4", "mkdirp": "^2.1.5", "mocha": "^9.0.2", "rimraf": "^4.3.1", "stream-bench": "^0.1.2", "tar": "^6.1.13", "yauzl": "^2.9.0"}, "keywords": ["archive", "archiver", "stream", "zip", "tar"], "publishConfig": {"registry": "https://registry.npmjs.org/"}}