{"name": "vite-plugin-electron", "version": "0.29.0", "description": "Electron 🔗 Vite", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./plugin": {"types": "./dist/plugin.d.ts", "import": "./dist/plugin.mjs", "require": "./dist/plugin.js"}, "./simple": {"types": "./dist/simple.d.ts", "import": "./dist/simple.mjs", "require": "./dist/simple.js"}, "./*": "./*"}, "repository": {"type": "git", "url": "git+https://github.com/electron-vite/vite-plugin-electron.git"}, "author": "<PERSON>(草鞋没号) <<EMAIL>>", "license": "MIT", "packageManager": "pnpm@8.0.0", "scripts": {"dev": "vite build --watch", "build": "vite build", "types": "tsc", "test": "vitest run", "prepublishOnly": "npm run build && npm run test"}, "peerDependencies": {"vite-plugin-electron-renderer": "*"}, "peerDependenciesMeta": {"vite-plugin-electron-renderer": {"optional": true}}, "devDependencies": {"rollup": "^4.13.0", "typescript": "^5.4.2", "vite": "^5.1.6", "vite-plugin-electron-renderer": "^0.14.5", "vitest": "^1.3.1"}, "files": ["dist", "electron-env.d.ts", "plugin.d.ts", "simple.d.ts"], "keywords": ["vite", "plugin", "electron", "renderer"]}