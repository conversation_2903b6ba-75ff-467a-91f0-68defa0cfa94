{"name": "augment_activator", "version": "1.0.0", "description": "Electron应用程序", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "cross-env NODE_ENV=production electron-builder", "build:mac": "cross-env NODE_ENV=production electron-builder --mac", "build:mac-arm64": "cross-env NODE_ENV=production electron-builder --mac --arm64", "build:mac-x64": "cross-env NODE_ENV=production electron-builder --mac --x64", "build:prod": "cross-env NODE_ENV=production electron-builder", "build:prod:mac": "cross-env NODE_ENV=production electron-builder --mac", "build:prod:mac-arm64": "cross-env NODE_ENV=production electron-builder --mac --arm64", "build:prod:mac-x64": "cross-env NODE_ENV=production electron-builder --mac --x64", "dist": "npm run build:prod", "pack": "electron-builder --dir", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["electron"], "author": "", "license": "ISC", "devDependencies": {"concurrently": "^9.2.0", "cross-env": "^7.0.3", "electron": "^36.4.0", "electron-builder": "^24.13.3", "vite-plugin-electron": "^0.29.0"}, "dependencies": {"@vitejs/plugin-vue": "^6.0.0", "archiver": "^7.0.1", "sqlite3": "^5.1.7", "vite": "^5.4.19", "vue": "^3.5.17"}, "build": {"appId": "com.xunhewenhua.augment-activator", "productName": "Augment账号分发系统", "directories": {"output": "dist"}, "files": ["**/*", "!free-augment/**/*", "!test_*.html", "!*.md", "!.giti<PERSON>re", "!icon.png"], "mac": {"target": [{"target": "dmg", "arch": ["arm64"]}, {"target": "zip", "arch": ["arm64"]}], "category": "public.app-category.developer-tools"}, "dmg": {"title": "Augment账号分发系统"}}}