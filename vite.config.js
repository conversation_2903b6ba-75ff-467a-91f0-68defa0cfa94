import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  
  // 构建配置
  build: {
    outDir: 'dist-vue',
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'vue-src/index.html')
      }
    },
    // 为 Electron 优化
    target: 'chrome100',
    minify: 'esbuild',
    sourcemap: false
  },
  
  // 开发服务器配置
  server: {
    port: 3000,
    strictPort: true
  },
  
  // 路径解析
  resolve: {
    alias: {
      '@': resolve(__dirname, 'vue-src'),
      '@components': resolve(__dirname, 'vue-src/components'),
      '@lib': resolve(__dirname, 'src/lib'),
      '@assets': resolve(__dirname, 'src/assets')
    }
  },
  
  // 为 Electron 环境配置
  base: './',
  
  // CSS 配置
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/styles/variables.scss";`
      }
    }
  }
})
